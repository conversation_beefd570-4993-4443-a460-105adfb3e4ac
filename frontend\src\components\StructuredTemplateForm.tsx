import React, { useState, useEffect } from 'react';
import { FaSave, FaDownload, FaSpinner } from 'react-icons/fa';
import SignaturePad from './SignaturePad';

interface TableStructure {
  index: number;
  has_fields: boolean;
  fields: Array<{
    name: string;
    label: string;
    row: number;
    cell: number;
    type: string;
    cell_text: string;
  }>;
  structure: Array<{
    row_index: number;
    cells: Array<{
      text: string;
      row: number;
      col: number;
      has_field: boolean;
      fields: any[];
    }>;
    fields: any[];
    is_header: boolean;
  }>;
  headers: string[][];
  table_type: string;
  layout: string;
}

interface StructuredTemplateFormProps {
  templateId: number;
  templateName: string;
  tables: TableStructure[];
  fields: Array<{
    name: string;
    label: string;
    type: string;
  }>;
  checkboxes: Array<{
    name: string;
    label: string;
    type: string;
  }>;
  onSave: (formData: Record<string, any>) => void;
  onCancel: () => void;
  generating?: boolean;
}

const StructuredTemplateForm: React.FC<StructuredTemplateFormProps> = ({
  templateId,
  templateName,
  tables,
  fields,
  checkboxes,
  onSave,
  onCancel,
  generating = false
}) => {
  const [formData, setFormData] = useState<Record<string, any>>({});

  // Initialize form data
  useEffect(() => {
    const initialData: Record<string, any> = {};

    // Initialize all fields from regular fields and checkboxes
    [...fields, ...checkboxes].forEach(field => {
      if (field.type === 'checkbox') {
        initialData[field.name] = false;
      } else if (field.type === 'date') {
        initialData[field.name] = new Date().toISOString().split('T')[0];
      } else {
        initialData[field.name] = '';
      }
    });

    // Initialize fields from tables
    tables.forEach(table => {
      table.fields.forEach(field => {
        const shouldBeCheckbox = field.type === 'checkbox' ||
          field.name.toLowerCase().includes('goed') ||
          field.name.toLowerCase().includes('fout') ||
          field.name.toLowerCase().includes('nvt');

        if (shouldBeCheckbox) {
          initialData[field.name] = false;
        } else if (field.type === 'date') {
          initialData[field.name] = new Date().toISOString().split('T')[0];
        } else {
          initialData[field.name] = '';
        }
      });
    });

    console.log('Initialized form data:', initialData);
    setFormData(initialData);
  }, [fields, checkboxes, tables]);

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const handleSubmit = () => {
    console.log('Submitting form data:', formData);

    // Validate that we have some data
    const hasData = Object.values(formData).some(value => {
      if (typeof value === 'boolean') return true;
      if (typeof value === 'string') return value.trim() !== '';
      return value != null;
    });

    if (!hasData) {
      console.warn('No form data to submit');
    }

    onSave(formData);
  };

  const renderBasicInfoTable = (table: TableStructure) => {
    if (table.table_type !== 'form_table') return null;

    return (
      <div key={table.index} className="mb-8">
        <div className="bg-white dark:bg-dark-input border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
          <table className="w-full">
            <tbody>
              {table.structure.map((row, rowIndex) => (
                <tr key={rowIndex} className={row.is_header ? 'bg-gray-50 dark:bg-dark-secondary' : ''}>
                  {row.cells.map((cell, cellIndex) => (
                    <td key={cellIndex} className="border border-gray-200 dark:border-gray-600 p-3">
                      {cell.has_field ? (
                        <div>
                          {cell.fields.map((field, fieldIndex) => (
                            <div key={fieldIndex}>
                              <label className="block text-sm font-medium text-gray-700 dark:text-dark-text mb-1">
                                {field.label}
                              </label>
                              <input
                                type={field.type === 'email' ? 'email' : field.type === 'tel' ? 'tel' : 'text'}
                                className="input input-bordered w-full"
                                value={formData[field.name] || ''}
                                onChange={(e) => handleFieldChange(field.name, e.target.value)}
                                disabled={generating}
                                placeholder={`Vul ${field.label.toLowerCase()} in`}
                              />
                            </div>
                          ))}
                        </div>
                      ) : (
                        <span className="font-medium text-gray-700 dark:text-dark-text">
                          {cell.text}
                        </span>
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderCheckboxTable = (table: TableStructure) => {
    if (table.table_type !== 'checkbox_table') return null;

    // Group fields by their row
    const fieldsByRow: Record<number, any[]> = {};
    table.fields.forEach(field => {
      if (!fieldsByRow[field.row]) {
        fieldsByRow[field.row] = [];
      }
      fieldsByRow[field.row].push(field);
    });

    return (
      <div key={table.index} className="mb-8">
        <div className="bg-white dark:bg-dark-input border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
          <table className="w-full">
            <thead>
              {table.headers.map((headerRow, headerIndex) => (
                <tr key={headerIndex} className="bg-amspm-blue text-white">
                  {headerRow.map((header, cellIndex) => (
                    <th key={cellIndex} className="border border-gray-300 p-3 text-left font-semibold">
                      {header}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {table.structure.map((row, rowIndex) => {
                if (row.is_header) return null;
                
                return (
                  <tr key={rowIndex} className="hover:bg-gray-50 dark:hover:bg-dark-secondary">
                    {row.cells.map((cell, cellIndex) => (
                      <td key={cellIndex} className="border border-gray-200 dark:border-gray-600 p-3">
                        {cell.has_field ? (
                          <div className="flex flex-col space-y-2">
                            {cell.fields.map((field, fieldIndex) => {
                              // Determine if this should be a checkbox based on field name and context
                              const shouldBeCheckbox = field.type === 'checkbox' ||
                                field.name.toLowerCase().includes('goed') ||
                                field.name.toLowerCase().includes('fout') ||
                                field.name.toLowerCase().includes('nvt');

                              const shouldBeTextarea = field.name.toLowerCase().includes('opmerkingen') ||
                                field.name.toLowerCase().includes('opmerking');

                              return (
                                <div key={fieldIndex}>
                                  {shouldBeCheckbox ? (
                                    <div className="flex items-center justify-center">
                                      <input
                                        type="checkbox"
                                        className="checkbox checkbox-primary"
                                        checked={formData[field.name] || false}
                                        onChange={(e) => handleFieldChange(field.name, e.target.checked)}
                                        disabled={generating}
                                      />
                                    </div>
                                  ) : field.type === 'signature' ? (
                                    <SignaturePad
                                      value={formData[field.name] || ''}
                                      onChange={(signature) => handleFieldChange(field.name, signature)}
                                      disabled={generating}
                                      label=""
                                    />
                                  ) : shouldBeTextarea ? (
                                    <textarea
                                      className="textarea textarea-bordered w-full text-sm"
                                      value={formData[field.name] || ''}
                                      onChange={(e) => handleFieldChange(field.name, e.target.value)}
                                      disabled={generating}
                                      placeholder="Opmerkingen..."
                                      rows={2}
                                    />
                                  ) : (
                                    <input
                                      type={field.type === 'date' ? 'date' : field.type === 'email' ? 'email' : field.type === 'tel' ? 'tel' : 'text'}
                                      className="input input-bordered w-full text-sm"
                                      value={formData[field.name] || ''}
                                      onChange={(e) => handleFieldChange(field.name, e.target.value)}
                                      disabled={generating}
                                      placeholder={field.label}
                                    />
                                  )}
                                </div>
                              );
                            })}
                          </div>
                        ) : (
                          <span className="font-medium text-gray-700 dark:text-dark-text">
                            {cell.text}
                          </span>
                        )}
                      </td>
                    ))}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderSignatureTable = (table: TableStructure) => {
    if (table.table_type !== 'signature_table') return null;

    return (
      <div key={table.index} className="mb-8">
        <div className="bg-white dark:bg-dark-input border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
          <table className="w-full">
            <tbody>
              {table.structure.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {row.cells.map((cell, cellIndex) => (
                    <td key={cellIndex} className="border border-gray-200 dark:border-gray-600 p-4">
                      {cell.has_field ? (
                        <div>
                          {cell.fields.map((field, fieldIndex) => (
                            <div key={fieldIndex}>
                              <label className="block text-sm font-medium text-gray-700 dark:text-dark-text mb-2">
                                {field.label}
                              </label>
                              {field.type === 'signature' ? (
                                <SignaturePad
                                  value={formData[field.name] || ''}
                                  onChange={(signature) => handleFieldChange(field.name, signature)}
                                  disabled={generating}
                                  label=""
                                />
                              ) : (
                                <input
                                  type={field.type === 'date' ? 'date' : 'text'}
                                  className="input input-bordered w-full"
                                  value={formData[field.name] || ''}
                                  onChange={(e) => handleFieldChange(field.name, e.target.value)}
                                  disabled={generating}
                                  placeholder={field.label}
                                />
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <span className="font-semibold text-gray-700 dark:text-dark-text">
                          {cell.text}
                        </span>
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white dark:bg-dark-card rounded-lg shadow-lg">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-2xl font-bold text-amspm-text dark:text-dark-text">
            {templateName}
          </h2>
          <p className="text-gray-600 dark:text-dark-text-light mt-1">
            Vul het formulier in en sla het op als document
          </p>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={onCancel}
            className="btn btn-outline"
            disabled={generating}
          >
            Annuleren
          </button>
          <button
            onClick={handleSubmit}
            className="btn btn-primary"
            disabled={generating}
          >
            {generating ? (
              <><FaSpinner className="animate-spin mr-2" /> Opslaan...</>
            ) : (
              <><FaSave className="mr-2" /> Opslaan</>
            )}
          </button>
        </div>
      </div>

      {/* Form Content */}
      <div className="p-6 max-h-[80vh] overflow-y-auto">
        {tables.map(table => (
          <div key={table.index}>
            {renderBasicInfoTable(table)}
            {renderCheckboxTable(table)}
            {renderSignatureTable(table)}
          </div>
        ))}
      </div>
    </div>
  );
};

export default StructuredTemplateForm;
