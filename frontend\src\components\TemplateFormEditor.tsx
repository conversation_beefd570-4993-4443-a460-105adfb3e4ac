import React, { useState, useEffect } from 'react';
import { DocumentTemplate } from '../types/document_template';
import { FaSave, FaDownload, FaSpinner } from 'react-icons/fa';
import TemplateService from '../services/templateService';
import SignaturePad from './SignaturePad';
import StructuredTemplateForm from './StructuredTemplateForm';
import api from '../api';

interface TemplateFormEditorProps {
  template: DocumentTemplate;
  customerId?: number;
  onSave: (blob: Blob | null, fileName: string, document?: any) => void;
  onCancel: () => void;
  testMode?: boolean;
}

interface FormField {
  name: string;
  label: string;
  value: string;
  type: 'text' | 'checkbox' | 'date' | 'textarea' | 'email' | 'tel' | 'number' | 'time' | 'signature';
}

const TemplateFormEditor: React.FC<TemplateFormEditorProps> = ({
  template,
  customerId,
  onSave,
  onCancel,
  testMode = false
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [fields, setFields] = useState<FormField[]>([]);
  const [templateContent, setTemplateContent] = useState<ArrayBuffer | null>(null);
  const [generating, setGenerating] = useState<boolean>(false);
  const [templateAnalysis, setTemplateAnalysis] = useState<any>(null);
  const [useStructuredForm, setUseStructuredForm] = useState(false);

  useEffect(() => {
    loadTemplate();
  }, [template.id]);

  const loadTemplate = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the enhanced backend analysis
      const analysis = await TemplateService.analyzeTemplateFromBackend(template.id);
      setTemplateAnalysis(analysis);

      // Determine if we should use structured form
      const hasStructuredTables = analysis.has_structured_tables ||
        analysis.tables.some(table => ['checkbox_table', 'signature_table', 'form_table'].includes(table.table_type));
      setUseStructuredForm(hasStructuredTables);

      // Create form fields from analysis
      const formFields: FormField[] = [
        // Regular fields
        ...analysis.fields.map(field => ({
          name: field.name,
          label: field.label,
          value: '',
          type: field.type as FormField['type']
        })),
        // Checkbox fields
        ...analysis.checkboxes.map(field => ({
          name: field.name,
          label: field.label,
          value: 'false',
          type: 'checkbox' as const
        })),
        // Table fields (only if not using structured form)
        ...(hasStructuredTables ? [] : analysis.tables.flatMap(table =>
          table.fields.map(field => ({
            name: field.name,
            label: `${field.label} (Table ${table.index + 1})`,
            value: '',
            type: field.type as FormField['type']
          }))
        ))
      ];

      setFields(formFields);

      // Also load the template content for filling
      const content = await TemplateService.loadTemplate(template.id);
      setTemplateContent(content);
    } catch (err: any) {
      setError(`Failed to load template: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = (index: number, value: string) => {
    const updatedFields = [...fields];
    updatedFields[index].value = value;
    setFields(updatedFields);
  };

  const handleCheckboxChange = (index: number, checked: boolean) => {
    const updatedFields = [...fields];
    updatedFields[index].value = checked ? 'true' : 'false';
    setFields(updatedFields);
  };

  const handleStructuredFormSave = async (formData: Record<string, any>) => {
    if (!customerId) return;

    try {
      setGenerating(true);
      setError(null);

      // Process form data to ensure proper format
      const processedFormData: Record<string, any> = {};

      for (const [key, value] of Object.entries(formData)) {
        // Convert checkbox values to boolean
        if (typeof value === 'boolean') {
          processedFormData[key] = value;
        } else if (typeof value === 'string' && (value === 'true' || value === 'false')) {
          processedFormData[key] = value === 'true';
        } else {
          processedFormData[key] = value;
        }
      }

      console.log('Sending form data to backend:', processedFormData);

      const response = await TemplateService.generateDocumentFromTemplate(
        template.id,
        customerId,
        processedFormData
      );

      console.log('Backend response:', response);

      if (response) {
        onSave(null, '', response);
      } else {
        setError('Failed to generate document');
      }
    } catch (err: any) {
      console.error('Error generating document:', err);
      setError(err.response?.data?.error || err.message || 'Failed to generate document');
    } finally {
      setGenerating(false);
    }
  };

  const createTemplateData = () => {
    const data: Record<string, any> = {};

    fields.forEach(field => {
      if (field.type === 'checkbox') {
        // For checkboxes, we'll use true/false values
        data[field.name] = field.value === 'true';
      } else {
        data[field.name] = field.value;
      }
    });

    return data;
  };

  // Group fields by category for better organization
  const groupFields = () => {
    const basicFields: FormField[] = [];
    const installationFields: FormField[] = [];
    const centraleFields: FormField[] = [];
    const detectieFields: FormField[] = [];
    const bekabelingFields: FormField[] = [];
    const signaleringFields: FormField[] = [];
    const doormeldingFields: FormField[] = [];
    const signatureFields: FormField[] = [];
    const otherFields: FormField[] = [];

    fields.forEach(field => {
      const name = field.name.toLowerCase();

      if (['bonnummer', 'klantnummer', 'telefoon', 'contactpersoon', 'bedrijf', 'email', 'adres', 'type', 'datum', 'begin_tijd', 'eind_tijd', 'klant_naam', 'monteur_naam'].includes(name)) {
        basicFields.push(field);
      } else if (name.includes('inbraak') || name.includes('brand') || name.includes('cctv')) {
        installationFields.push(field);
      } else if (name.includes('centrale')) {
        centraleFields.push(field);
      } else if (name.includes('detectie')) {
        detectieFields.push(field);
      } else if (name.includes('bekabeling')) {
        bekabelingFields.push(field);
      } else if (name.includes('signalering')) {
        signaleringFields.push(field);
      } else if (name.includes('doormelding')) {
        doormeldingFields.push(field);
      } else if (field.type === 'signature') {
        signatureFields.push(field);
      } else {
        otherFields.push(field);
      }
    });

    return {
      basicFields,
      installationFields,
      centraleFields,
      detectieFields,
      bekabelingFields,
      signaleringFields,
      doormeldingFields,
      signatureFields,
      otherFields
    };
  };

  const handleSave = async () => {
    if (!customerId) {
      setError('Customer ID is required');
      return;
    }

    try {
      setGenerating(true);
      setError(null);

      // Create template data from form fields
      const data = createTemplateData();

      // Use the backend service to generate the document
      const document = await TemplateService.generateDocumentFromTemplate(
        template.id,
        customerId,
        data
      );

      // Call the onSave callback with the document info
      onSave(null, document.name, document);
    } catch (err: any) {
      setError(`Failed to generate document: ${err.message || 'Unknown error'}`);
    } finally {
      setGenerating(false);
    }
  };

  const handleSaveLocal = async () => {
    if (!customerId) {
      setError('Customer ID is required for local save');
      return;
    }

    try {
      setGenerating(true);
      setError(null);

      // Create template data from form fields
      const data = createTemplateData();

      // Use the backend service to generate the document
      const response = await api.post(`/document-templates/${template.id}/generate`, {
        customer_id: customerId,
        form_data: data
      }, {
        responseType: 'blob' // Important: get the response as a blob
      });

      // Generate a filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${template.name.replace(/\s+/g, '_')}_${timestamp}.docx`;

      // Create blob from response
      const docxBlob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });

      // Pass the blob and filename to the parent component
      onSave(docxBlob, fileName);
    } catch (err: any) {
      setError(`Failed to generate document: ${err.message || 'Unknown error'}`);
    } finally {
      setGenerating(false);
    }
  };

  const handleDownload = async () => {
    if (!customerId) {
      setError('Customer ID is required for download');
      return;
    }

    try {
      setGenerating(true);
      setError(null);

      // Create template data from form fields
      const data = createTemplateData();

      // Use the backend service to generate the document for download
      const response = await api.post(`/document-templates/${template.id}/generate-download`, {
        customer_id: customerId,
        form_data: data
      }, {
        responseType: 'blob' // Important: get the response as a blob
      });

      // Generate a filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${template.name.replace(/\s+/g, '_')}_${timestamp}.docx`;

      // Create blob and download
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });

      // Download the document using file-saver
      TemplateService.downloadDocument(blob, fileName);
    } catch (err: any) {
      setError(`Failed to download document: ${err.message || 'Unknown error'}`);
    } finally {
      setGenerating(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <FaSpinner className="animate-spin text-amspm-primary text-2xl" />
        <span className="ml-2">Loading template...</span>
      </div>
    );
  }

  // Use structured form for templates with proper table structure
  if (useStructuredForm && templateAnalysis && templateAnalysis.tables) {
    return (
      <StructuredTemplateForm
        templateId={template.id}
        templateName={template.name}
        tables={templateAnalysis.tables}
        fields={templateAnalysis.fields || []}
        checkboxes={templateAnalysis.checkboxes || []}
        onSave={handleStructuredFormSave}
        onCancel={onCancel}
        generating={generating}
      />
    );
  }

  const getCompletionStats = () => {
    const totalFields = fields.length;
    const filledFields = fields.filter(field => {
      if (field.type === 'checkbox') return true; // Checkboxes are always "complete"
      return field.value && field.value.trim() !== '';
    }).length;

    return { totalFields, filledFields, percentage: totalFields > 0 ? Math.round((filledFields / totalFields) * 100) : 0 };
  };

  const stats = getCompletionStats();

  return (
    <div className="bg-white dark:bg-dark-card shadow-lg rounded-lg">
      {/* Header Section */}
      <div className="border-b border-gray-200 dark:border-gray-700 p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-2xl font-bold text-amspm-text dark:text-dark-text mb-2">
              📝 {template.name}
            </h2>
            <p className="text-gray-600 dark:text-dark-text-light">
              Vul onderstaande velden in om het document te genereren
            </p>
          </div>
          <button
            onClick={onCancel}
            className="btn btn-outline"
            disabled={generating}
          >
            ← Terug
          </button>
        </div>

        {/* Progress indicator */}
        <div className="mb-4">
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-dark-text-light mb-2">
            <span>Voortgang invullen</span>
            <span>{stats.filledFields} van {stats.totalFields} velden ingevuld ({stats.percentage}%)</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-amspm-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${stats.percentage}%` }}
            ></div>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex space-x-3">
          <button
            onClick={handleDownload}
            className="btn btn-outline"
            disabled={generating}
            title="Download Document (Local Processing)"
          >
            <FaDownload className="mr-2" /> Download
          </button>
          {customerId ? (
            <button
              onClick={handleSave}
              className="btn btn-primary"
              disabled={generating}
              title="Save to Customer Documents (Backend Processing)"
            >
              {generating ? (
                <><FaSpinner className="animate-spin mr-2" /> Opslaan...</>
              ) : (
                <><FaSave className="mr-2" /> Opslaan bij Klant</>
              )}
            </button>
          ) : (
            <button
              onClick={handleSaveLocal}
              className="btn btn-primary"
              disabled={generating}
              title="Generate Document (Local Processing)"
            >
              {generating ? (
                <><FaSpinner className="animate-spin mr-2" /> {testMode ? 'Testen...' : 'Genereren...'}</>
              ) : (
                <><FaSave className="mr-2" /> {testMode ? 'Test Document' : 'Genereer Document'}</>
              )}
            </button>
          )}
        </div>
      </div>

      {/* Form Content */}
      <div className="p-6">
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <p className="text-red-600 dark:text-red-400">{error}</p>
          </div>
        )}

        <div className="max-h-[70vh] overflow-y-auto pr-2">
        {fields.length === 0 ? (
          <p className="text-gray-500 dark:text-dark-text-light italic">
            No form fields found in this template. Make sure your template contains fields in the format {'{field_name}'}.
          </p>
        ) : (
          <div className="space-y-8">
            {(() => {
              const groupedFields = groupFields();

              const renderFieldGroup = (title: string, groupFields: FormField[], icon?: string) => {
                if (groupFields.length === 0) return null;

                return (
                  <div key={title} className="border border-gray-100 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-dark-secondary">
                    <h3 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-4 flex items-center">
                      {icon && <span className="mr-2">{icon}</span>}
                      {title}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {groupFields.map((field, index) => {
                        const globalIndex = fields.findIndex(f => f.name === field.name);
                        return (
                          <div key={field.name} className="form-control">
                            <label className="label">
                              <span className="label-text text-amspm-text dark:text-dark-text font-medium">
                                {field.label}
                              </span>
                            </label>

                            {field.type === 'checkbox' ? (
                              <div className="flex items-center">
                                <input
                                  type="checkbox"
                                  className="checkbox checkbox-primary"
                                  checked={field.value === 'true'}
                                  onChange={(e) => handleCheckboxChange(globalIndex, e.target.checked)}
                                  disabled={generating}
                                />
                                <span className="ml-2 text-sm text-gray-500 dark:text-dark-text-light">
                                  {field.value === 'true' ? 'Ja' : 'Nee'}
                                </span>
                              </div>
                            ) : field.type === 'date' ? (
                              <input
                                type="date"
                                className="input input-bordered w-full"
                                value={field.value}
                                onChange={(e) => handleFieldChange(globalIndex, e.target.value)}
                                disabled={generating}
                              />
                            ) : field.type === 'time' ? (
                              <input
                                type="time"
                                className="input input-bordered w-full"
                                value={field.value}
                                onChange={(e) => handleFieldChange(globalIndex, e.target.value)}
                                disabled={generating}
                              />
                            ) : field.type === 'textarea' ? (
                              <textarea
                                className="textarea textarea-bordered w-full"
                                value={field.value}
                                onChange={(e) => handleFieldChange(globalIndex, e.target.value)}
                                disabled={generating}
                                placeholder={`Vul ${field.label.toLowerCase()} in`}
                                rows={3}
                              />
                            ) : field.type === 'signature' ? (
                              <div className="col-span-2">
                                <SignaturePad
                                  value={field.value}
                                  onChange={(signature) => handleFieldChange(globalIndex, signature)}
                                  disabled={generating}
                                  label=""
                                />
                              </div>
                            ) : (
                              <input
                                type={field.type === 'email' ? 'email' : field.type === 'tel' ? 'tel' : field.type === 'number' ? 'number' : 'text'}
                                className="input input-bordered w-full"
                                value={field.value}
                                onChange={(e) => handleFieldChange(globalIndex, e.target.value)}
                                disabled={generating}
                                placeholder={`Vul ${field.label.toLowerCase()} in`}
                              />
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              };

              return (
                <>
                  {renderFieldGroup("📋 Basis Informatie", groupedFields.basicFields)}
                  {renderFieldGroup("🔧 Type Installatie", groupedFields.installationFields)}
                  {renderFieldGroup("⚡ Centrale / Kiezer", groupedFields.centraleFields)}
                  {renderFieldGroup("🔍 Detectie", groupedFields.detectieFields)}
                  {renderFieldGroup("🔌 Bekabeling", groupedFields.bekabelingFields)}
                  {renderFieldGroup("🚨 Signalering", groupedFields.signaleringFields)}
                  {renderFieldGroup("📡 Doormelding", groupedFields.doormeldingFields)}
                  {renderFieldGroup("✍️ Handtekeningen", groupedFields.signatureFields)}
                  {groupedFields.otherFields.length > 0 && renderFieldGroup("📝 Overige", groupedFields.otherFields)}
                </>
              );
            })()}
          </div>
        )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-dark-secondary rounded-b-lg">
          <p className="text-sm text-gray-600 dark:text-dark-text-light text-center">
            💡 Tip: Vul alle velden in voor het beste resultaat. Handtekeningen kunnen met muis of vinger worden gezet.
          </p>
        </div>
      </div>
    </div>
  );
};

export default TemplateFormEditor;
