"""
Document Template service module.
This module provides business logic for document templates.
"""
from typing import List, Dict, Optional, BinaryIO
import os
import logging
import datetime as dt
import re
from app.repositories.document_template_repository import DocumentTemplateRepository
from app.schemas.document_template_schema import document_template_schema, document_templates_schema
from flask import send_file
from werkzeug.utils import secure_filename

logger = logging.getLogger(__name__)

class DocumentTemplateService:
    """Service for document templates."""

    def __init__(self):
        """Initialize the service."""
        self.template_repo = DocumentTemplateRepository()
    
    def get_all_templates(self) -> List[Dict]:
        """Get all document templates."""
        templates = self.template_repo.get_all()
        return document_templates_schema.dump(templates)
    
    def get_template_by_id(self, template_id: int) -> Optional[Dict]:
        """Get a document template by ID."""
        template = self.template_repo.get_by_id(template_id)
        if not template:
            return None
        return document_template_schema.dump(template)
    
    def get_templates_by_document_type(self, document_type: str) -> List[Dict]:
        """Get all document templates for a specific document type."""
        templates = self.template_repo.get_by_document_type(document_type)
        return document_templates_schema.dump(templates)
    
    def create_template(self, template_data: Dict, file) -> Dict:
        """Create a new document template."""
        # Determine file type from file extension
        original_filename = file.filename or f"template_{template_data.get('document_type', 'document')}.docx"
        filename = secure_filename(original_filename)
        file_ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else 'docx'
        
        # Add file type to template data
        template_data['file_type'] = file_ext
        
        # Create the template
        template = self.template_repo.create(template_data, file)
        return document_template_schema.dump(template)
    
    def update_template(self, template_id: int, template_data: Dict) -> Dict:
        """Update a document template."""
        template = self.template_repo.get_by_id(template_id)
        if not template:
            raise ValueError(f"Template with ID {template_id} not found")
        
        updated_template = self.template_repo.update(template, template_data)
        return document_template_schema.dump(updated_template)
    
    def delete_template(self, template_id: int) -> bool:
        """Delete a document template."""
        template = self.template_repo.get_by_id(template_id)
        if not template:
            raise ValueError(f"Template with ID {template_id} not found")
        
        return self.template_repo.delete(template)
    
    def get_template_file(self, template_id: int):
        """Get the template file from Firebase Storage."""
        from firebase_admin import storage
        from flask import redirect

        template = self.template_repo.get_by_id(template_id)
        if not template:
            raise ValueError(f"Template with ID {template_id} not found")

        # Generate a signed URL for the template file
        bucket = storage.bucket()
        blob = bucket.blob(template.file_path)

        # Generate a signed URL that expires after 1 hour
        signed_url = blob.generate_signed_url(
            version="v4",
            expiration=3600,  # 1 hour in seconds
            method="GET"
        )

        # Redirect to the signed URL
        return redirect(signed_url)

    def get_template_file_content(self, template_id: int):
        """Get the template file content directly from Firebase Storage."""
        from firebase_admin import storage
        from flask import Response
        import mimetypes

        template = self.template_repo.get_by_id(template_id)
        if not template:
            raise ValueError(f"Template with ID {template_id} not found")

        # Get the file content from Firebase Storage
        bucket = storage.bucket()
        blob = bucket.blob(template.file_path)

        if not blob.exists():
            raise ValueError(f"Template file not found in storage: {template.file_path}")

        # Download the file content
        file_content = blob.download_as_bytes()

        # Determine the content type
        content_type, _ = mimetypes.guess_type(template.name)
        if not content_type:
            content_type = 'application/octet-stream'

        # Return the file content as a response
        return Response(
            file_content,
            mimetype=content_type,
            headers={
                'Content-Disposition': f'attachment; filename="{template.name}"',
                'Content-Length': str(len(file_content))
            }
        )

    def analyze_template_fields(self, template_id: int) -> Dict:
        """Analyze a template to extract field information."""
        from firebase_admin import storage
        import zipfile
        import xml.etree.ElementTree as ET
        import re
        import io

        template = self.template_repo.get_by_id(template_id)
        if not template:
            raise ValueError(f"Template with ID {template_id} not found")

        # Download the template file from Firebase Storage
        bucket = storage.bucket()
        blob = bucket.blob(template.file_path)

        # Download as bytes
        template_content = blob.download_as_bytes()

        # Analyze the DOCX file
        fields_info = self._extract_fields_from_docx(template_content)

        return {
            'template_id': template_id,
            'template_name': template.name,
            'document_type': template.document_type,
            'fields': fields_info['fields'],
            'checkboxes': fields_info['checkboxes'],
            'tables': fields_info['tables'],
            'has_structured_tables': fields_info.get('has_structured_tables', False)
        }

    def _extract_fields_from_docx(self, docx_content: bytes) -> Dict:
        """Extract field information from DOCX content."""
        import zipfile
        import xml.etree.ElementTree as ET
        import re
        import io

        fields = []
        checkboxes = []
        tables = []

        try:
            # Open the DOCX file as a ZIP
            with zipfile.ZipFile(io.BytesIO(docx_content), 'r') as docx_zip:
                # Read the main document XML
                document_xml = docx_zip.read('word/document.xml').decode('utf-8')

                # Parse XML
                root = ET.fromstring(document_xml)

                # Define namespaces
                namespaces = {
                    'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
                    'w14': 'http://schemas.microsoft.com/office/word/2010/wordml'
                }

                # Extract simple text fields {field_name}
                simple_field_pattern = r'\{([^{}#/^]+)\}'
                simple_matches = re.findall(simple_field_pattern, document_xml)

                for match in simple_matches:
                    field_name = match.strip()
                    # Skip conditional fields and negative conditionals
                    if not any(keyword in field_name.lower() for keyword in ['if', 'else', '#', '/', '^']) and not field_name.startswith('^'):
                        field_type = self._determine_field_type(field_name)
                        fields.append({
                            'name': field_name,
                            'label': self._format_field_label(field_name),
                            'type': field_type,
                            'required': True
                        })

                # Extract conditional fields (checkboxes) {#field_name} or {#if field_name}
                checkbox_pattern = r'\{#(?:if\s+)?([^{}]+)\}'
                checkbox_matches = re.findall(checkbox_pattern, document_xml)

                for match in checkbox_matches:
                    field_name = match.strip()
                    if not any(keyword in field_name.lower() for keyword in ['else', '/', '^']):
                        checkboxes.append({
                            'name': field_name,
                            'label': self._format_field_label(field_name),
                            'type': 'checkbox',
                            'required': False
                        })

                # Extract negative conditional fields (checkboxes) {^field_name}
                negative_checkbox_pattern = r'\{\^([^{}]+)\}'
                negative_checkbox_matches = re.findall(negative_checkbox_pattern, document_xml)

                for match in negative_checkbox_matches:
                    field_name = match.strip()
                    if not any(keyword in field_name.lower() for keyword in ['else', '/', '#']):
                        # Only add if not already added from positive conditional
                        if not any(cb['name'] == field_name for cb in checkboxes):
                            checkboxes.append({
                                'name': field_name,
                                'label': self._format_field_label(field_name),
                                'type': 'checkbox',
                                'required': False
                            })

                # Extract table structures
                table_elements = root.findall('.//w:tbl', namespaces)
                for i, table in enumerate(table_elements):
                    table_info = self._analyze_table_structure(table, namespaces, i)
                    if table_info['has_fields']:
                        tables.append(table_info)

        except Exception as e:
            print(f"Error extracting fields from DOCX: {str(e)}")
            # Fallback to simple regex extraction
            return self._fallback_field_extraction(docx_content.decode('utf-8', errors='ignore'))

        # Remove duplicates
        fields = self._remove_duplicate_fields(fields)
        checkboxes = self._remove_duplicate_fields(checkboxes)

        # Add table context to fields
        for table in tables:
            for table_field in table['fields']:
                # Mark fields that are part of tables
                table_field['in_table'] = True
                table_field['table_index'] = table['index']
                table_field['table_type'] = table['table_type']

        return {
            'fields': fields,
            'checkboxes': checkboxes,
            'tables': tables,
            'has_structured_tables': any(table['table_type'] in ['checkbox_table', 'signature_table'] for table in tables)
        }

    def _determine_field_type(self, field_name: str) -> str:
        """Determine the appropriate field type based on field name."""
        field_lower = field_name.lower()

        if any(keyword in field_lower for keyword in ['datum', 'date']):
            return 'date'
        elif any(keyword in field_lower for keyword in ['email', 'e-mail']):
            return 'email'
        elif any(keyword in field_lower for keyword in ['telefoon', 'phone', 'tel']):
            return 'tel'
        elif any(keyword in field_lower for keyword in ['nummer', 'number', 'bedrag', 'prijs']):
            return 'number'
        elif any(keyword in field_lower for keyword in ['tijd', 'time']):
            return 'time'
        elif any(keyword in field_lower for keyword in ['opmerking', 'beschrijving', 'notitie']):
            return 'textarea'
        elif any(keyword in field_lower for keyword in ['handtekening', 'signature']):
            return 'signature'
        else:
            return 'text'

    def _format_field_label(self, field_name: str) -> str:
        """Format field name into a readable label."""
        # Replace underscores with spaces
        label = field_name.replace('_', ' ')

        # Capitalize first letter of each word
        label = ' '.join(word.capitalize() for word in label.split())

        return label

    def _analyze_table_structure(self, table_element, namespaces: Dict, table_index: int) -> Dict:
        """Analyze table structure to identify form fields and preserve layout."""
        rows = table_element.findall('.//w:tr', namespaces)
        table_fields = []
        has_fields = False
        table_structure = []
        headers = []

        for row_index, row in enumerate(rows):
            cells = row.findall('.//w:tc', namespaces)
            row_data = []
            row_fields = []

            for cell_index, cell in enumerate(cells):
                cell_text = self._extract_cell_text(cell, namespaces)

                # Store cell information
                cell_info = {
                    'text': cell_text,
                    'row': row_index,
                    'col': cell_index,
                    'has_field': False,
                    'fields': []
                }

                # Check if cell contains template fields
                field_matches = re.findall(r'\{([^{}]+)\}', cell_text)
                if field_matches:
                    has_fields = True
                    cell_info['has_field'] = True

                    for match in field_matches:
                        field_name = match.strip()

                        # Skip conditional syntax markers (these are not actual fields)
                        if field_name.startswith('#') or field_name.startswith('/') or field_name.startswith('^'):
                            continue

                        # Determine if this should be a checkbox based on context
                        field_type = self._determine_field_type_in_context(field_name, cell_text, table_structure)

                        field_info = {
                            'name': field_name,
                            'label': self._format_field_label(field_name),
                            'row': row_index,
                            'cell': cell_index,
                            'type': field_type,
                            'cell_text': cell_text
                        }
                        table_fields.append(field_info)
                        cell_info['fields'].append(field_info)
                        row_fields.append(field_info)

                row_data.append(cell_info)

            # Identify headers (usually first row or rows without fields)
            if row_index == 0 or (not row_fields and row_index < 3):
                headers.append([cell['text'] for cell in row_data])

            table_structure.append({
                'row_index': row_index,
                'cells': row_data,
                'fields': row_fields,
                'is_header': row_index == 0 or (not row_fields and row_index < 3)
            })

        # Determine table type and structure
        table_type = self._determine_table_type(table_structure, headers)

        return {
            'index': table_index,
            'has_fields': has_fields,
            'fields': table_fields,
            'structure': table_structure,
            'headers': headers,
            'table_type': table_type,
            'layout': 'table'
        }

    def _determine_table_type(self, table_structure: List, headers: List) -> str:
        """Determine the type of table based on its structure and content."""
        if not headers:
            return 'simple'

        # Check for checkbox-style tables (Goed, Fout, NVT columns)
        header_text = ' '.join([' '.join(header) for header in headers]).lower()
        if any(keyword in header_text for keyword in ['goed', 'fout', 'nvt', 'opmerkingen']):
            return 'checkbox_table'

        # Check for signature tables
        if any(keyword in header_text for keyword in ['handtekening', 'signature', 'akkoord', 'monteur']):
            return 'signature_table'

        # Check for form tables (basic info)
        if any(keyword in header_text for keyword in ['naam', 'adres', 'telefoon', 'email']):
            return 'form_table'

        return 'data_table'

    def _determine_field_type_in_context(self, field_name: str, cell_text: str, table_structure: List) -> str:
        """Determine field type based on context within table structure."""
        field_name_lower = field_name.lower()
        cell_text_lower = cell_text.lower()

        # Check if this is in a checkbox table context (Goed/Fout/NVT columns)
        checkbox_indicators = ['goed', 'fout', 'nvt']
        if any(indicator in field_name_lower for indicator in checkbox_indicators):
            return 'checkbox'

        # Check if the cell text suggests this should be a checkbox
        if any(indicator in cell_text_lower for indicator in checkbox_indicators):
            return 'checkbox'

        # Check surrounding context in table structure
        # If we're in a table with Goed/Fout/NVT headers, fields in those columns should be checkboxes
        for row in table_structure:
            if row.get('is_header'):
                header_texts = [cell.get('text', '').lower() for cell in row.get('cells', [])]
                if any(indicator in ' '.join(header_texts) for indicator in checkbox_indicators):
                    # This table has checkbox-style headers
                    if any(indicator in field_name_lower for indicator in checkbox_indicators):
                        return 'checkbox'

        # Fall back to original field type determination
        return self._determine_field_type(field_name)

    def _extract_cell_text(self, cell_element, namespaces: Dict) -> str:
        """Extract text content from a table cell."""
        text_elements = cell_element.findall('.//w:t', namespaces)
        return ''.join(elem.text or '' for elem in text_elements)

    def _remove_duplicate_fields(self, fields: List[Dict]) -> List[Dict]:
        """Remove duplicate fields based on name."""
        seen_names = set()
        unique_fields = []

        for field in fields:
            if field['name'] not in seen_names:
                seen_names.add(field['name'])
                unique_fields.append(field)

        return unique_fields

    def _fallback_field_extraction(self, content: str) -> Dict:
        """Fallback method for field extraction using simple regex."""
        fields = []
        checkboxes = []

        # Simple field extraction
        simple_pattern = r'\{([^{}#/]+)\}'
        simple_matches = re.findall(simple_pattern, content)

        for match in simple_matches:
            field_name = match.strip()
            if not any(keyword in field_name.lower() for keyword in ['if', 'else']):
                fields.append({
                    'name': field_name,
                    'label': self._format_field_label(field_name),
                    'type': self._determine_field_type(field_name),
                    'required': True
                })

        # Checkbox extraction
        checkbox_pattern = r'\{#(?:if\s+)?([^{}]+)\}'
        checkbox_matches = re.findall(checkbox_pattern, content)

        for match in checkbox_matches:
            field_name = match.strip()
            if not any(keyword in field_name.lower() for keyword in ['else', '/', '^']):
                checkboxes.append({
                    'name': field_name,
                    'label': self._format_field_label(field_name),
                    'type': 'checkbox',
                    'required': False
                })

        return {
            'fields': self._remove_duplicate_fields(fields),
            'checkboxes': self._remove_duplicate_fields(checkboxes),
            'tables': []
        }

    def generate_document_from_template(self, template_id: int, customer_id: int, form_data: Dict, user_id: int) -> Dict:
        """Generate a document from a template with form data and customer information."""
        from firebase_admin import storage
        from app.utils.firebase import upload_file_to_storage
        from app.repositories.customer_repository import CustomerRepository
        from app.repositories.document_repository import DocumentRepository
        import tempfile
        import os
        import datetime as dt

        template = self.template_repo.get_by_id(template_id)
        if not template:
            raise ValueError(f"Template with ID {template_id} not found")

        # Get customer data
        customer_repo = CustomerRepository()
        customer = customer_repo.get_by_id(customer_id)
        if not customer:
            raise ValueError(f"Customer with ID {customer_id} not found")

        # Download template from Firebase Storage
        bucket = storage.bucket()
        blob = bucket.blob(template.file_path)
        template_content = blob.download_as_bytes()

        # Merge customer data with form data
        template_data = self._merge_customer_and_form_data(customer, form_data)

        # Fill the template
        filled_document = self._fill_docx_template(template_content, template_data)

        # Generate filename
        timestamp = int(dt.datetime.now(dt.timezone.utc).timestamp())
        company_name = getattr(customer, 'company_name', None) or customer.name
        filename = f"{timestamp}_{template.name}_{company_name}.docx"
        filename = filename.replace(' ', '_').replace('/', '_')

        # Upload filled document to Firebase Storage
        destination_path = f"documents/{customer_id}/{template.document_type}/{filename}"

        # Create temporary file for upload
        with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
            temp_file.write(filled_document)
            temp_file_path = temp_file.name

        try:
            # Upload to Firebase Storage
            with open(temp_file_path, 'rb') as file:
                file_url, storage_path = upload_file_to_storage(file, destination_path)

            # Create document record
            document_repo = DocumentRepository()
            document = document_repo.create(
                customer_id=customer_id,
                event_id=None,
                file_url=file_url,
                file_path=storage_path,
                name=filename,
                document_type=template.document_type,
                uploaded_by=user_id,
                expiry_date=None,
                related_document_id=None,
                version_status="active"
            )

            # Clear cache for customer documents
            from app.utils.cache_utils import clear_cache_for_entity
            clear_cache_for_entity('customer_documents', customer_id)

            # Log successful document creation
            logger.info(f"Document template generated successfully: ID {document.id}, Customer {customer_id}, Template {template_id}")

            return document.to_dict()

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    def _merge_customer_and_form_data(self, customer, form_data: Dict) -> Dict:
        """Merge customer data with form data for template filling."""
        # Start with form data
        template_data = dict(form_data)

        # Get company name safely (Customer model doesn't have company_name field)
        company_name = getattr(customer, 'company_name', None) or customer.name

        # Add customer data with common field mappings
        customer_mappings = {
            'klantnaam': company_name,
            'naam': customer.name,
            'bedrijfsnaam': company_name,
            'adres': getattr(customer, 'address', ''),
            'postcode': getattr(customer, 'postal_code', ''),
            'plaats': getattr(customer, 'city', ''),
            'telefoon': getattr(customer, 'phone', ''),
            'email': getattr(customer, 'email', ''),
            'klantnummer': str(customer.id) if customer.id else '',
            'bonnummer': str(customer.id) if customer.id else '',
            'contactpersoon': getattr(customer, 'contact_person', '') or customer.name,
        }

        # Add customer data, but don't override form data
        for key, value in customer_mappings.items():
            if key not in template_data and value:
                template_data[key] = value

        # Add current date fields
        now = dt.datetime.now()
        date_mappings = {
            'datum': now.strftime('%d-%m-%Y'),
            'datum_vandaag': now.strftime('%d-%m-%Y'),
            'huidige_datum': now.strftime('%d-%m-%Y'),
        }

        for key, value in date_mappings.items():
            if key not in template_data:
                template_data[key] = value

        # Process checkbox fields - convert boolean values to proper format
        # This ensures that checkbox fields work with conditional template syntax
        processed_data = {}
        for key, value in template_data.items():
            processed_data[key] = value

            # If this is a boolean field, ensure it's properly formatted
            if isinstance(value, bool):
                processed_data[key] = value
            elif isinstance(value, str) and value.lower() in ['true', 'false']:
                processed_data[key] = value.lower() == 'true'

            # For checkbox fields ending with _goed, _fout, _nvt, ensure boolean conversion
            if key.endswith(('_goed', '_fout', '_nvt')):
                if isinstance(value, str):
                    processed_data[key] = value.lower() in ['true', '1', 'yes', 'ja']
                else:
                    processed_data[key] = bool(value)

        return processed_data

    def _fill_docx_template(self, template_content: bytes, data: Dict) -> bytes:
        """Fill a DOCX template with data using DocxTemplater-like functionality."""
        import zipfile
        import io
        import re

        # Create a copy of the template
        template_zip = zipfile.ZipFile(io.BytesIO(template_content), 'r')
        output_buffer = io.BytesIO()
        output_zip = zipfile.ZipFile(output_buffer, 'w', zipfile.ZIP_DEFLATED, compresslevel=6)

        try:
            for file_info in template_zip.infolist():
                try:
                    file_content = template_zip.read(file_info.filename)

                    # Process XML files that might contain template fields
                    if file_info.filename.endswith('.xml'):
                        try:
                            # Decode with error handling
                            content_str = file_content.decode('utf-8')
                            processed_content = self._replace_template_fields(content_str, data)
                            file_content = processed_content.encode('utf-8')
                        except UnicodeDecodeError:
                            # If UTF-8 fails, try other encodings
                            try:
                                content_str = file_content.decode('utf-8', errors='replace')
                                processed_content = self._replace_template_fields(content_str, data)
                                file_content = processed_content.encode('utf-8')
                            except Exception as e:
                                logger.warning(f"Failed to process XML file {file_info.filename}: {e}")
                                # Keep original content if processing fails
                                pass

                    # Preserve file info attributes
                    output_zip.writestr(file_info, file_content)

                except Exception as e:
                    logger.error(f"Error processing file {file_info.filename}: {e}")
                    # Try to write original content
                    try:
                        file_content = template_zip.read(file_info.filename)
                        output_zip.writestr(file_info, file_content)
                    except:
                        pass

        except Exception as e:
            logger.error(f"Error filling DOCX template: {e}")
            raise
        finally:
            template_zip.close()
            output_zip.close()

        return output_buffer.getvalue()

    def _replace_template_fields(self, content: str, data: Dict) -> str:
        """Replace template fields in XML content."""
        import re

        # Replace simple fields {field_name}
        def replace_simple_field(match):
            field_name = match.group(1).strip()
            field_value = data.get(field_name, '')

            # Handle signature fields (base64 images)
            if field_name.lower().endswith('handtekening') and field_value.startswith('data:image'):
                # For now, just indicate that a signature was provided
                # In a full implementation, you'd embed the image in the DOCX
                return '[HANDTEKENING AANWEZIG]'

            return str(field_value)

        content = re.sub(r'\{([^{}#/]+)\}', replace_simple_field, content)

        # Replace conditional fields {#if field_name}...{/if}
        def replace_conditional_field(match):
            field_name = match.group(1).strip()
            inner_content = match.group(2)

            # Check if field exists and is truthy
            field_value = data.get(field_name, False)
            if field_value and str(field_value).lower() not in ['false', '0', 'no', 'nee']:
                # Process inner content for nested fields
                return self._replace_template_fields(inner_content, data)
            else:
                return ''

        content = re.sub(r'\{#if\s+([^}]+)\}(.*?)\{/if\}', replace_conditional_field, content, flags=re.DOTALL)

        # Replace simple conditional fields {#field_name}...{/field_name}
        def replace_simple_conditional(match):
            field_name = match.group(1).strip()
            inner_content = match.group(2)

            field_value = data.get(field_name, False)
            if field_value and str(field_value).lower() not in ['false', '0', 'no', 'nee']:
                return self._replace_template_fields(inner_content, data)
            else:
                return ''

        content = re.sub(r'\{#([^}]+)\}(.*?)\{/\1\}', replace_simple_conditional, content, flags=re.DOTALL)

        # Replace negative conditional fields {^field_name}...{/field_name} (show when false)
        def replace_negative_conditional(match):
            field_name = match.group(1).strip()
            inner_content = match.group(2)

            field_value = data.get(field_name, False)
            if not field_value or str(field_value).lower() in ['false', '0', 'no', 'nee']:
                return self._replace_template_fields(inner_content, data)
            else:
                return ''

        # Use a more specific pattern that matches the exact field name
        content = re.sub(r'\{\^([^}]+)\}(.*?)\{/\1\}', replace_negative_conditional, content, flags=re.DOTALL)

        # Also handle the simpler case where the closing tag doesn't repeat the field name
        content = re.sub(r'\{\^([^}]+)\}(.*?)\{/[^}]*\}', replace_negative_conditional, content, flags=re.DOTALL)

        return content
